- choices:
    - index: 0
      message:
        role: assistant
        content: |
          {
            "message": "This is a test",
            "goal": "test goal",
            "title": "test title",
            "steps": [
              {
                "id": "1",
                "description": "test search tools"
              }
            ]
          }

- choices:
    - index: 0
      message:
        role: assistant
        content: null
        tool_calls:
          - type: function
            function:
              name: info_search_web
              arguments: |
                {
                    "query": "latest AI developments 2024",
                    "date_range": "past_month"
                }
      finish_reason: stop

- choices:
    - index: 0
      message:
        role: assistant
        content: |
          test message end

- choices:
    - index: 0
      message:
        role: assistant
        content: |
          {
            "steps": []
          }