services:
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: build-stage  # Use build stage image instead of production image
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]  # Start in development mode and listen on all network interfaces
    volumes:
      - ./frontend:/app  # Mount source code directory
      - /app/node_modules  # Avoid overwriting container's node_modules
    ports:
      - "5173:5173"  # Vite default development port
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://127.0.0.1:8000
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - manus-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: ["./dev.sh"]  # Start in reload mode
    volumes:
      - ./backend:/app  # Mount source code directory
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /app/__pycache__  # Avoid overwriting cache files
      - /app/.venv  # Avoid overwriting virtual environment
    ports:
      - "8000:8000"
    depends_on:
      sandbox: 
        condition: service_started
        required: false
      mongodb:
        condition: service_started
        required: true
    restart: unless-stopped
    networks:
      - manus-network
    env_file:
      - .env
    environment:
      - SANDBOX_ADDRESS=sandbox # Use single container as sandbox

  sandbox:
    build:
      context: ./sandbox
      dockerfile: Dockerfile
    hostname: sandbox
    volumes:
      - ./sandbox:/app  # Mount source code directory
      - ./sandbox/supervisord.conf:/etc/supervisor/conf.d/app.conf
      - /app/__pycache__  # Avoid overwriting cache files
      - /app/.venv  # Avoid overwriting virtual environment
    ports:
      #- "9222:9222"
      - "5902:5900"
      #- "5901:5901"
      - "8080:8080"
    environment:
      - UVI_ARGS="--reload"
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
    restart: unless-stopped
    networks:
      - manus-network

  mockserver:
    build:
      context: ./mockserver
      dockerfile: Dockerfile
    volumes:
      - ./mockserver:/app  # Mount source code directory
      - /app/__pycache__  # Avoid overwriting cache files
      - /app/.venv  # Avoid overwriting virtual environment
    restart: unless-stopped
    environment:
      - MOCK_DATA_FILE=default.yaml
      - MOCK_DELAY=1
    networks:
      - manus-network

  mongodb:
    image: mongo:7.0
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped
    ports:
      - "27017:27017"
    networks:
      - manus-network

  redis:
    image: redis:7.0
    restart: unless-stopped
    networks:
      - manus-network

volumes:
  mongodb_data:
    name: manus-mongodb-data

networks:
  manus-network:
    name: manus-network
    driver: bridge
