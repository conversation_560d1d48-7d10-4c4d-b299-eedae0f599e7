{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "type-check": "vue-tsc", "preview": "vite preview"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@novnc/novnc": "^1.5.0", "@types/dompurify": "^3.2.0", "@vueuse/core": "^10.1.2", "axios": "^1.8.4", "clsx": "^2.0.0", "dompurify": "^3.2.5", "framer-motion": "^10.12.16", "lucide-vue-next": "^0.511.0", "marked": "^15.0.8", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "tailwind-merge": "^1.13.1", "vue": "^3.3.4", "vue-i18n": "^9.14.4", "vue-router": "^4.2.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.2", "typescript": "^5.1.3", "vite": "^4.3.9", "vite-plugin-monaco-editor": "^1.1.0", "vue-tsc": "^1.6.5"}}