<template>

    <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none"
        style="animation: 4s linear 0s infinite reverse none running spin;">
        <mask id="path-1-inside-1_1527_82619" fill="white">
            <path
                d="M38 19C38 29.4934 29.4934 38 19 38C8.50659 38 0 29.4934 0 19C0 8.50659 8.50659 0 19 0C29.4934 0 38 8.50659 38 19Z">
            </path>
        </mask>
        <g clip-path="url(#paint0_angular_1527_82619_clip_path)" data-figma-skip-parse="true"
            mask="url(#path-1-inside-1_1527_82619)">
            <g transform="matrix(0 0.019 -0.019 0 19 19)">
                <foreignObject x="-1105.26" y="-1105.26" width="2210.53" height="2210.53">
                    <div
                        style="background: conic-gradient(from 90deg, rgb(0, 129, 242) 0deg, rgba(0, 129, 242, 0) 360deg); height: 100%; width: 100%; opacity: 1;">
                    </div>
                </foreignObject>
            </g>
        </g>
        <path
            d="M36 19C36 28.3888 28.3888 36 19 36V40C30.598 40 40 30.598 40 19H36ZM19 36C9.61116 36 2 28.3888 2 19H-2C-2 30.598 7.40202 40 19 40V36ZM2 19C2 9.61116 9.61116 2 19 2V-2C7.40202 -2 -2 7.40202 -2 19H2ZM19 2C28.3888 2 36 9.61116 36 19H40C40 7.40202 30.598 -2 19 -2V2Z"
            data-figma-gradient-fill="{&quot;type&quot;:&quot;GRADIENT_ANGULAR&quot;,&quot;stops&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.0,&quot;g&quot;:0.50588238239288330,&quot;b&quot;:0.94901961088180542,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:0.0,&quot;g&quot;:0.50588238239288330,&quot;b&quot;:0.94901961088180542,&quot;a&quot;:0.0},&quot;position&quot;:1.0}],&quot;stopsVar&quot;:[{&quot;color&quot;:{&quot;r&quot;:0.0,&quot;g&quot;:0.50588238239288330,&quot;b&quot;:0.94901961088180542,&quot;a&quot;:1.0},&quot;position&quot;:0.0},{&quot;color&quot;:{&quot;r&quot;:0.0,&quot;g&quot;:0.50588238239288330,&quot;b&quot;:0.94901961088180542,&quot;a&quot;:0.0},&quot;position&quot;:1.0}],&quot;transform&quot;:{&quot;m00&quot;:2.3268289405024801e-15,&quot;m01&quot;:-38.0,&quot;m02&quot;:38.0,&quot;m10&quot;:38.0,&quot;m11&quot;:2.3268289405024801e-15,&quot;m12&quot;:-2.3268289405024801e-15},&quot;opacity&quot;:1.0,&quot;blendMode&quot;:&quot;NORMAL&quot;,&quot;visible&quot;:true}"
            mask="url(#path-1-inside-1_1527_82619)">
        </path>
        <defs>
            <clipPath id="paint0_angular_1527_82619_clip_path">
                <path
                    d="M36 19C36 28.3888 28.3888 36 19 36V40C30.598 40 40 30.598 40 19H36ZM19 36C9.61116 36 2 28.3888 2 19H-2C-2 30.598 7.40202 40 19 40V36ZM2 19C2 9.61116 9.61116 2 19 2V-2C7.40202 -2 -2 7.40202 -2 19H2ZM19 2C28.3888 2 36 9.61116 36 19H40C40 7.40202 30.598 -2 19 -2V2Z"
                    mask="url(#path-1-inside-1_1527_82619)">
                </path>
            </clipPath>
        </defs>
    </svg>
</template>



<style>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>