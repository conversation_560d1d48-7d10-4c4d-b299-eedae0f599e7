[unix_http_server]
file=/tmp/supervisor.sock   ; Path of UNIX socket file, supervisor uses it to listen
chmod=0770                  ; Socket file permissions
chown=ubuntu:ubuntu         ; Socket file owner and group

[supervisord]
logfile=/dev/stdout         ; Main log file output to standard output
logfile_maxbytes=0          ; No limit on log size
loglevel=info               ; Log level
pidfile=/tmp/supervisord.pid ; Path of pidfile
nodaemon=true                ; Need to run in foreground in Docker
minfds=1024                  ; Minimum value of open file descriptors
minprocs=200                 ; Minimum number of processes
autoshutdown=true            ; Auto exit after all services stop

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock ; Socket connection to supervisord

; Xvfb virtual display configuration
[program:xvfb]
command=bash -c "rm -f /tmp/.X1-lock && Xvfb :1 -screen 0 1280x1029x24"
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
environment=DISPLAY=:1
priority=10

; Google Chrome configuration
[program:chrome]
command=chromium \
    --display=:1 \
    --window-size=1280,1029 \
    --start-maximized \
    --no-sandbox \
    --disable-dev-shm-usage \
    --disable-setuid-sandbox \
    --disable-accelerated-2d-canvas \
    --disable-gpu \
    --disable-features=WelcomeExperience,SigninPromo \
    --no-first-run \
    --no-default-browser-check \
    --disable-infobars \
    --test-type \
    --disable-popup-blocking \
    --disable-gpu-sandbox \
    --no-xshm \
    --new-window=false \
    --disable-notifications \
    --disable-extensions \
    --disable-component-extensions-with-background-pages \
    --disable-popup-blocking \
    --disable-prompt-on-repost \
    --disable-dialogs \
    --disable-modal-dialogs \
    --disable-web-security \
    --disable-site-isolation-trials \
    --remote-debugging-address=0.0.0.0 \
    --remote-debugging-port=8222 %(ENV_CHROME_ARGS)s
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
environment=DISPLAY=:1
priority=20
startretries=3
startsecs=5

; socat port forwarding configuration
[program:socat]
command=socat TCP-LISTEN:9222,bind=0.0.0.0,fork,reuseaddr TCP:127.0.0.1:8222
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
priority=30
startsecs=2

; VNC service configuration
[program:x11vnc]
command=x11vnc -display :1 -nopw -shared -listen 0.0.0.0 -xkb -forever -rfbport 5900
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
environment=DISPLAY=:1
priority=40
startsecs=3

; Websockify configuration - Convert VNC to WebSocket
[program:websockify]
command=websockify 0.0.0.0:5901 localhost:5900
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
priority=45
startsecs=3

; FastAPI application configuration
[program:app]
command=uvicorn app.main:app --host 0.0.0.0 --port 8080 %(ENV_UVI_ARGS)s
directory=/app
user=ubuntu
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
environment=HOME=/home/<USER>
priority=50

; Group configuration, can start or stop multiple programs at once
[group:services]
programs=xvfb,chrome,socat,x11vnc,websockify,app 